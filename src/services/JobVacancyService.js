const { QdrantClient } = require('@qdrant/js-client-rest');
const AppService = require('./AppService');
const OnetService = require('./OnetService');
const {
  JobVacancy,
  JobTitle,
  User,
  VacancyGroupVariable,
  LlmMetadata,
  sequelize,
} = require('../models');
const JobVacanciesRepository = require('../repositories/JobVacanciesRepository');
const GenerateJobDescService = require('./job_vacancy/GenerateJobDescService');
const GoogleAiService = require('./external/GoogleAiService');

class JobVacancyService extends AppService {
  constructor() {
    super();
    this.repository = new JobVacanciesRepository();
    this.onetService = new OnetService();
    this.googleAiService = new GoogleAiService();
    this.qdrantClient = new QdrantClient({
      url: this.config.qdrantUrl,
      apiKey: this.config.qdrantApiKey,
    });
    this.generateJobDescService = new GenerateJobDescService({
      onetService: this.onetService,
      googleAiService: this.googleAiService,
      qdrantClient: this.qdrantClient,
    });
  }

  /**
   * Get all job vacancies
   * @param {Object} params - Query params (page, limit, filters, etc.)
   * @returns {Object} Job vacancies array and pagination info
   */
  async findAll(params = {}) {
    params.includes = ['jobLevel'];
    const { rows, pagination } = await this.repository.findAll(params);

    const jobVacancyIds = rows.map(row => row.id);
    const countsById = await this.getUjvCounts(jobVacancyIds);

    return { job_vacancies: rows, pagination, countsById };
  }

  /**
   * Find a job vacancy by ID
   * @param {number} id - Job vacancy ID
   * @returns {Object}
   * @throws {NotFoundError} If job vacancy is not found
   */
  async findById(id) {
    const vacancy = await this.repository.findOne({ id, includes: ['jobLevel'] });
    this.exists(vacancy, 'Job vacancy not found');

    let referenceUsers = [];
    if (vacancy.related_user_ids) {
      referenceUsers = await User.findAll({
        where: { id: vacancy.related_user_ids },
        attributes: ['id', 'name'],
      });
    }

    return { vacancy, referenceUsers };
  }

  /**
   * Create a new job vacancy
   * @param {Object} data - Job vacancy data
   * @returns {Object} Created job vacancy
   * @throws {NotFoundError} If job title is not found
   */
  async create(data) {
    // Fetch job title name if job_title_id is provided but name is not
    let jobTitleName = data.name;
    if (!jobTitleName && data.job_title_id) {
      const jobTitle = await JobTitle.findByPk(data.job_title_id);
      this.exists(jobTitle, 'Job title not found');
      jobTitleName = jobTitle.name;
    }

    const vacancyData = {
      ...data,
      name: jobTitleName,
      status: 'generating_jobdesc',
    };

    const vacancy = await JobVacancy.create(vacancyData);
    this.setJobDesc(vacancy);

    return vacancy;
  }

  /**
   * Update a job vacancy
   * @param {number} id - Job vacancy ID
   * @param {Object} data - Job vacancy data
   * @returns {Object} Updated job vacancy
   * @throws {NotFoundError} If job vacancy is not found
   */
  async update(id, data) {
    const vacancy = await this.repository.findOne({ id });
    this.exists(vacancy, 'Job vacancy not found');

    const followupAction = data.followup_action;
    delete data.followup_action;

    if (followupAction) {
      const isDraft = vacancy.status === 'draft';
      const isActive = vacancy.status === 'active';
      const errorMessage = `Cannot update this vacancy, current status: ${vacancy.status}`;
      this.assert(isDraft || isActive, errorMessage);
    }

    const actionMapping = {
      generate_jobdesc: 'generating_jobdesc',
      generate_job_variables: 'generating_job_variables',
    };

    const additionalData = {};
    additionalData.status = actionMapping[followupAction];

    const vacancyData = {
      ...data,
      ...additionalData,
    };

    await vacancy.update(vacancyData);

    if (followupAction === 'generate_jobdesc') {
      this.setJobDesc(vacancy);
    } else if (followupAction === 'generate_job_variables') {
      this.setVacancyGroupVariables(vacancy);
    }

    return vacancy;
  }

  async setJobDesc(vacancy) {
    try {
      const jobTitleName = vacancy.name;
      const relatedUserIds = vacancy.related_user_ids;
      const jobLevelName = vacancy.jobLevel ? vacancy.jobLevel.name : '';
      const roleSummary = vacancy.role_summary;

      const generatedJobDesc = await this.generateJobDescService.generateJobDesc(
        jobTitleName,
        relatedUserIds,
        jobLevelName,
        roleSummary,
      );

      await vacancy.update({
        job_desc: generatedJobDesc.jobDescription.key_responsibilities,
        related_onetsoc_codes: generatedJobDesc.onetsocCodes,
        status: 'draft',
        detailed_descriptions: generatedJobDesc.jobDescription,
      });
    } catch (error) {
      console.error('Error in setJobDesc:', error);
      await vacancy.update({ status: 'draft' });
      // If we has error alerting, we should add them here
    }
  }

  async setVacancyGroupVariables(vacancy) {
    try {
      const ksao = await this.generateKsao(vacancy);

      const vgvRecords = await this.ksaoMatching({
        ksao,
        jobVacancyId: vacancy.id,
      });

      if (vgvRecords.length > 0) {
        await VacancyGroupVariable.bulkCreate(vgvRecords, {
          updateOnDuplicate: ['keyword_match_count', 'keyword_total_count', 'match_type', 'weight'],
        });
      }

      await vacancy.update({
        ksao,
        status: 'draft',
      });
    } catch (error) {
      console.error('Error setting vacancy group variables:', error);
      await vacancy.update({ status: 'draft' });
    }
  }

  async generateKsao(vacancy) {
    const vacancyName = vacancy.name;
    const jobDesc = vacancy.job_desc;
    const jobDetails = vacancy.detailed_descriptions;
    const onetsocCodes = vacancy.related_onetsoc_codes;
    const occupations = await this.getOccupationsData(onetsocCodes);

    const [systemPrompt, userPrompt] = await Promise.all([
      this.getSystemPrompt(),
      this.getUserPrompt(vacancyName, jobDesc, occupations, jobDetails),
    ]);

    const aiParams = {
      model: 'gemini-2.5-flash',
      contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
      config: {
        temperature: 0.2,
        responseMimeType: 'application/json',
        thinkingConfig: { thinkingBudget: -1 },
        systemInstruction: [{ text: systemPrompt }],
      },
    };

    const response = await this.googleAiService.generateContent(aiParams);

    LlmMetadata.create({
      request: aiParams,
      responses: response,
      action_type: 'generate_ksao',
    });

    const ksao = JSON.parse(response.candidates[0].content.parts[0].text);
    return ksao;
  }

  async getOccupationsData(onetsocCodes) {
    const onetResults = await Promise.all([
      this.onetService.getOccupations(onetsocCodes),
      this.onetService.getKnowledges(onetsocCodes),
      this.onetService.getSkills(onetsocCodes),
      this.onetService.getAbilities(onetsocCodes),
      this.onetService.getInterests(onetsocCodes),
      this.onetService.getWorkValues(onetsocCodes),
      this.onetService.getWorkStyles(onetsocCodes),
    ]);

    const occupations = onetResults[0];
    const referenceData = {
      knowledges: onetResults[1],
      skills: onetResults[2],
      abilities: onetResults[3],
      interests: onetResults[4],
      work_values: onetResults[5],
      work_styles: onetResults[6],
    };

    await Promise.all(
      Object.keys(referenceData).map(async key => {
        await Promise.all(
          referenceData[key].map(async item => {
            const onetsocCode = item.onetsoc_code;
            delete item.onetsoc_code;

            occupations[onetsocCode][key] ||= [];
            occupations[onetsocCode][key].push(item);
          }),
        );
      }),
    );

    return occupations;
  }

  async getSystemPrompt() {
    return Promise.resolve(`
      ## Role and Context
      You are an expert HR professional specializing in job analysis and competency modeling. Your expertise includes translating job requirements into structured Knowledge, Skills, Abilities, and Other characteristics (KSAO) frameworks that align with industry standards and O*NET occupational data.

      ## Task Overview
      Create a comprehensive KSAO profile for a specific job vacancy by analyzing job descriptions and leveraging O*NET occupational data as supporting reference material.

      ## Input Structure
      You will receive:
      1. **Job Title**: The specific position title
      2. **Job Descriptions**: Detailed list of key job responsibilities, qualifications, skill and competencies, and success metrics
      3. **Related O*NET Data**: Occupational information including:
        - Knowledge areas
        - Skills requirements
        - Abilities needed
        - Work interests
        - Work values
        - Work styles

      ## Instructions

      ### Step 1: Analysis
      - Carefully analyze the provided job descriptions to identify core responsibilities and requirements
      - Cross-reference with O*NET data to identify relevant competencies
      - Prioritize job-specific requirements while using O*NET data to fill gaps and validate decisions

      ### Step 2: KSAO Development
      Create four distinct categories using these definitions and formatting guidelines:

      **Knowledge (K)**: Body of facts and information someone must know
      - Write as short, specific topic areas or subject matter domains
      - Use concise phrases (3-8 words typically)
      - Focus on what someone needs to know, not how they apply it
      - Examples: "Brand positioning and messaging", "Employment law and regulations", "Financial analysis principles"

      **Skills (S)**: Learned proficiencies that can be demonstrated
      - Write as actionable capabilities or techniques
      - Use verb phrases when appropriate
      - Focus on what someone can do or perform
      - Examples: "Develop brand strategy", "Conduct market research", "Data analysis and reporting"

      **Abilities (A)**: Enduring attributes that support performance
      - Write as inherent capacities or traits
      - Often start with "Ability to..." but keep concise
      - Focus on cognitive, physical, or interpersonal capabilities
      - Examples: "Strategic thinking", "Ability to influence stakeholders", "Analytical reasoning"

      **Other Characteristics (O)**: Traits, motivations, values, or work styles
      - Write as personal attributes or orientations
      - Focus on personality traits, work preferences, and motivational factors
      - Examples: "Creative mindset", "Detail orientation", "Growth mindset"

      ### Step 3: Quality Standards
      - Keep each item concise (typically 2-6 words, maximum 8 words)
      - Ensure items are specific and job-relevant
      - Avoid lengthy explanations or examples in the KSAO items themselves
      - Maintain clear distinction between categories
      - Aim for 6-10 items per category for comprehensive coverage
      - Prioritize the most critical competencies for job success

      ## Output Format
      Return your response as a valid JSON object with the following structure:
      {
        "knowledges": [
          "string"
        ],
        "skills": [
          "string"
        ],
        "abilities": [
          "string"
        ],
        "other_characteristics": [
          "string"
        ]
      }

      ## Example Output (Brand Manager)
      {
        "knowledges": [
          "Brand positioning and messaging",
          "Market research methodologies",
          "Competitor analysis",
          "Consumer behavior insights",
          "Brand guidelines development",
          "Marketing analytics tools",
          "Digital marketing channels",
          "Campaign planning processes"
        ],
        "skills": [
          "Develop brand strategy",
          "Conduct market research",
          "Create brand guidelines",
          "Monitor brand metrics",
          "Data analysis and reporting",
          "Communication and storytelling",
          "Cross-functional collaboration",
          "Stakeholder presentation"
        ],
        "abilities": [
          "Strategic thinking",
          "Analytical reasoning",
          "Creative problem-solving",
          "Ability to influence stakeholders",
          "Ability to balance creativity with data",
          "Cross-functional collaboration",
          "Adaptability to changing markets"
        ],
        "other_characteristics": [
          "Creative mindset",
          "Passion for brand building",
          "Growth mindset",
          "Consumer empathy",
          "Detail orientation",
          "Leadership presence",
          "Innovation-focused"
        ]
      }

      ## Important Notes
      - Base your KSAO primarily on the job descriptions provided
      - Use O*NET data as supporting reference to enhance and validate your analysis
      - Keep all items concise and specific - avoid lengthy explanations
      - Focus on the most essential competencies for job success
      - Ensure all items are directly relevant to the specific job vacancy
      - Maintain professional HR terminology and standards
      - Double-check that your output is valid JSON format
    `);
  }

  async getUserPrompt(vacancyName, jobDesc, occupations, jobDetails) {
    let userPrompt = `# Job Title\n${vacancyName}\n\n`;

    // higher function
    const mapJobDetails = array => {
      let result = '';
      array.forEach(item => {
        result += `- ${item}\n\n`;
      });
      return result;
    };

    if (jobDetails) {
      userPrompt += `# Job Details\n${jobDetails}\n\n`;
      userPrompt += '## Job Key Responsibilities\n';
      userPrompt += mapJobDetails(jobDetails.key_responsibilities);
      userPrompt += '## Job Qualifications\n';
      userPrompt += mapJobDetails(jobDetails.qualifications);
      userPrompt += '## Job Skill & Competencies\n';
      userPrompt += mapJobDetails(jobDetails.competencies);
      userPrompt += '## Job Success Metrics\n';
      userPrompt += mapJobDetails(jobDetails.success_metrics);
    } else {
      userPrompt += `# Job Descriptions\n`;
      userPrompt += mapJobDetails(jobDesc);
    }

    userPrompt += '\n# Related O*NET Data\n';

    await Promise.all(
      Object.values(occupations).map(async occupation => {
        userPrompt += `## Occupation: ${occupation.title}\n`;
        userPrompt += `### Description\n${occupation.description}\n\n`;

        await Promise.all(
          ['knowledges', 'skills', 'abilities', 'interests', 'work_values', 'work_styles'].map(
            async key => {
              if (occupation[key] && occupation[key].length > 0) {
                userPrompt += `### ${key.replace('_', ' ')}\n`;
                userPrompt += `\`\`\`json\n`;
                userPrompt += JSON.stringify(occupation[key], null, 2);
                userPrompt += `\n\`\`\`\n\n`;
              }
            },
          ),
        );
      }),
    );

    return Promise.resolve(userPrompt);
  }

  async ksaoMatching({ ksao, jobVacancyId }) {
    const vgvRecords = [];

    const [jgvs, flattenedKsao] = await Promise.all([
      this.getJobGroupVariables(),
      this.flattenKsao(ksao),
    ]);

    await Promise.all(
      jgvs.map(async jgv => {
        const matchCount = jgv.keywords.reduce((acc, keyword) => {
          return acc + (flattenedKsao.includes(keyword) ? 1 : 0);
        }, 0);

        vgvRecords.push({
          job_vacancy_id: jobVacancyId,
          job_group_variable_id: jgv.id,
          keyword_match_count: matchCount,
          keyword_total_count: jgv.keywords.length,
          match_type: 'weight',
          weight: 1 / jgvs.length,
        });
      }),
    );

    return vgvRecords;
  }

  async getJobGroupVariables() {
    return sequelize.query('SELECT id, keywords FROM job_group_variables', {
      type: sequelize.QueryTypes.SELECT,
    });
  }

  async flattenKsao(ksao) {
    return Object.values(ksao)
      .flat()
      .map(item => item.toLowerCase())
      .join(';');
  }

  async averagingVarGroup(jobVacancyId) {
    // Use a transaction for data integrity
    const transaction = await sequelize.transaction();

    try {
      // 1. FIX: Check if the job vacancy exists to prevent crashes
      const jobVacancy = await JobVacancy.findByPk(jobVacancyId, { transaction });
      if (!jobVacancy) {
        // It's good practice to rollback even on read-only failures
        await transaction.rollback();
        console.warn(`JobVacancy with id ${jobVacancyId} not found.`);
        return; // Or throw an error
      }

      const userIds = jobVacancy.related_user_ids;
      // Handle case where there are no related users
      if (!userIds || userIds.length === 0) {
        await transaction.rollback();
        console.log(`No related users for JobVacancy ${jobVacancyId}. Nothing to process.`);
        return;
      }

      // CTEs are now defined within the query string for clarity
      const singleSqlStatement = `
      WITH vacancy_groups AS (
        SELECT vgv.id AS vacancy_group_variable_id,
          ARRAY_AGG(jv.id) AS job_variable_ids
        FROM vacancy_group_variables vgv
        JOIN job_variables jv ON vgv.job_group_variable_id = jv.job_group_variable_id
        WHERE vgv.job_vacancy_id = :job_vacancy_id
          AND vgv.match_type = 'weight'
        GROUP BY vgv.id
      ), user_average_scores AS (
        SELECT ujv.user_id,
          vg.vacancy_group_variable_id,
          AVG(ujv.normalized_value) AS avg_group_score
        FROM user_job_variables ujv
        JOIN vacancy_groups vg ON ujv.job_variable_id = ANY (vg.job_variable_ids)
        GROUP BY ujv.user_id, vg.vacancy_group_variable_id
      ), baseline_medians AS (
        SELECT vg.vacancy_group_variable_id,
          CASE
            WHEN COUNT(uas.avg_group_score) = 0 THEN 12.5 -- Default value
            ELSE PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY uas.avg_group_score)
          END AS median_group_score
        FROM vacancy_groups vg
        LEFT JOIN user_average_scores uas
          ON vg.vacancy_group_variable_id = uas.vacancy_group_variable_id
          AND uas.user_id IN (:user_ids)
        GROUP BY vg.vacancy_group_variable_id
      ), user_match_scores AS (
        SELECT uas.user_id,
          vg.vacancy_group_variable_id,
          -- 2. RUNTIME FIX: Prevent division by zero error
          COALESCE(LEAST(uas.avg_group_score / NULLIF(vg.median_group_score, 0) * 100, 100), 0) AS average_match_score
        FROM user_average_scores uas
        JOIN baseline_medians vg ON uas.vacancy_group_variable_id = vg.vacancy_group_variable_id
      )
      INSERT
        INTO user_vacancy_group_variables (
          user_id,
          vacancy_group_variable_id,
          average_match_score,
          created_at,
          updated_at
        )
      SELECT user_id,
        vacancy_group_variable_id,
        average_match_score,
        NOW() AS created_at,
        NOW() AS updated_at
      FROM user_match_scores
      ON CONFLICT (user_id, vacancy_group_variable_id)
        DO UPDATE SET average_match_score = EXCLUDED.average_match_score,
          updated_at = EXCLUDED.updated_at;
    `;

      await sequelize.query(singleSqlStatement, {
        replacements: { job_vacancy_id: jobVacancyId, user_ids: userIds },
        type: sequelize.QueryTypes.RAW,
        transaction, // Pass the transaction to the query
      });

      // If everything succeeds, commit the transaction
      await transaction.commit();
      console.log(`Successfully averaged variable groups for JobVacancy ${jobVacancyId}.`);
    } catch (error) {
      // If any error occurs, rollback the transaction
      if (transaction) await transaction.rollback();
      console.error(`Failed to average variable groups for JobVacancy ${jobVacancyId}:`, error);
      // Re-throw the error to be handled by the calling function if necessary
      throw error;
    }
  }

  async weightingMatchRate(jobVacancyId) {
    const avgSql = `
      SELECT uvgv.user_id,
        SUM(uvgv.average_match_score * vgv.weight) AS match_rate
      FROM vacancy_group_variables vgv
      LEFT JOIN user_vacancy_group_variables uvgv ON vgv.id = uvgv.vacancy_group_variable_id
      WHERE vgv.job_vacancy_id = :job_vacancy_id
        AND vgv.match_type = 'weight'
        AND uvgv.user_id IS NOT NULL
      GROUP BY uvgv.user_id
    `;

    const upsertSql = `
      INSERT INTO user_job_vacancies (
        user_id,
        job_vacancy_id,
        match_rate,
        status,
        created_at,
        updated_at
      )
      SELECT user_id,
        :job_vacancy_id,
        match_rate,
        CASE
          WHEN match_rate >= 85 THEN 'matched'
          ELSE 'not_matched'
        END AS status,
        NOW() AS created_at,
        NOW() AS updated_at
      FROM (${avgSql}) uas
      ON CONFLICT (user_id, job_vacancy_id)
        DO UPDATE SET match_rate = EXCLUDED.match_rate,
          status = EXCLUDED.status,
          updated_at = EXCLUDED.updated_at;
    `;

    await sequelize.query(upsertSql, {
      replacements: { job_vacancy_id: jobVacancyId },
      type: sequelize.QueryTypes.RAW,
    });
  }

  async getUjvCounts(jobVacancyIds) {
    if (jobVacancyIds.length === 0) {
      return {};
    }

    const countSql = `
      SELECT job_vacancy_id,
        status,
        COUNT(*) AS ujv_count
      FROM user_job_vacancies
      WHERE job_vacancy_id IN (:job_vacancy_ids)
      GROUP BY job_vacancy_id, status;
    `;

    const countResult = await sequelize.query(countSql, {
      replacements: { job_vacancy_ids: jobVacancyIds },
      type: sequelize.QueryTypes.SELECT,
    });

    const countsById = jobVacancyIds.reduce((acc, jobVacancyId) => {
      acc[jobVacancyId] = {
        matched: 0,
        shortlisted: 0,
        approved: 0,
        appointed: 0,
        not_matched: 0,
      };
      return acc;
    }, {});

    countResult.forEach(count => {
      if (countsById[count.job_vacancy_id]) {
        countsById[count.job_vacancy_id][count.status] = count.ujv_count;
      }
    });

    return countsById;
  }
}

module.exports = JobVacancyService;
